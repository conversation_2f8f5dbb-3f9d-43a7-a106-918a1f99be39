import { TextField } from "@mui/material";
import { GamesContext } from "../context/GamesContext";
import type { IGames } from "../util/util";
import { useContext, useState } from "react";

export const NewGame = () => {
    const {setGames} = useContext(GamesContext);
    const [newGame, setnewGame] = useState<IGames>();
    function handleaddGame(){
        
    }
    return (
    <div className="">
        <TextField
            
            onChange={(e) => setnewGame({...setGames, : e.target.value})}
        />
        <TextField
            
            onChange={(e) => setnewGame({...setGames, : e.target.value})}
        />
        <TextField
            
            onChange={(e) => setnewGame({...setGames, : e.target.value})}
        />
        <Button onClick={handleaddGame}>Add quote </Button>
    </div>
    );
};