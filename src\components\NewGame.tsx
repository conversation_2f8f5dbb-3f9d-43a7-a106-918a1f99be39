import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { GamesContext } from "../context/GamesContext";
import type { IGames } from "../util/util";
import { useContext, useState } from "react";

export const NewGame = () => {
    const { addGames } = useContext(GamesContext);
    const [newGame, setNewGame] = useState<IGames>({
        name: "",
        genre: "",
        developer: "",
        publisher: "",
        rating: 0
    });

    function handleAddGame() {
        if (newGame.name && newGame.genre && newGame.developer && newGame.publisher && newGame.rating > 0) {
            addGames(newGame);
            // Reset form
            setNewGame({
                name: "",
                genre: "",
                developer: "",
                publisher: "",
                rating: 0
            });
        }
    }

    return (
        <div className="new-game-form">
            <h1>Add New Game</h1>
            <TextField
                label="Game Name"
                value={newGame.name}
                onChange={(e) => setNewGame({...newGame, name: e.target.value})}
                fullWidth
            />
            <TextField
                label="Genre"
                value={newGame.genre}
                onChange={(e) => setNewGame({...newGame, genre: e.target.value})}
                fullWidth
            />
            <TextField
                label="Developer"
                value={newGame.developer}
                onChange={(e) => setNewGame({...newGame, developer: e.target.value})}
                fullWidth
            />
            <TextField
                label="Publisher"
                value={newGame.publisher}
                onChange={(e) => setNewGame({...newGame, publisher: e.target.value})}
                fullWidth
            />
            <TextField
                label="Rating"
                type="number"
                value={newGame.rating}
                onChange={(e) => setNewGame({...newGame, rating: parseFloat(e.target.value) || 0})}
                inputProps={{ min: 0, max: 5, step: 0.1 }}
                fullWidth
            />
            <Button onClick={handleAddGame} variant="contained">Add Game</Button>
        </div>
    );
};