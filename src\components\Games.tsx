import { useContext, useState } from "react";
import type { IGames } from "../util/util";
import { GamesContext } from "../context/GamesContext";


export const Games = () => {
    const {games} = useContext(GamesContext);
    return (
    <div className="">
        {games.map((game, index) => (
            <Game key={index} game={game} />
        ))}
        
    </div>
    );
};

export const Game = (props: {game: IGames}) => {
    const game = props.game;
    return <div className="">
        
        <div className="">
            
            <h1 className="">{game.name}</h1>
            <p className="">{game.genre}</p>
            <p className="">{game.developer}</p>
            <p className="">{game.publisher}</p>
            <p className="">{game.rating}</p>
        </div>
        </div>
};