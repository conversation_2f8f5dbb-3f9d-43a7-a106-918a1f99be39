import { createContext, useEffect, useState, type ReactNode } from "react";
import type { IGames } from "../util/util";

interface IGameContext {
    games: IGames[];
    addGames:(newgame:IGames)=>void;
}

const defaultIgameCOntext:IGameContext = {
    games: [],
    addGames: (newgame:IGames) => {}
}

export const GamesContext = createContext<IGameContext>(defaultIgameCOntext);

export function GamesProvider({children}: {children:ReactNode}) {
    const [games, setGames] = useState<IGames[]>([]);

    async function getGames() {
        let res = await fetch('http://localhost:3001/api/games').then(res => res.json());
        setGames(res);
        
    }
    useEffect(() => {
        getGames();
    }, []);


async function addGames(newgame: IGames) {
    const res = await fetch(('http://localhost:3001/api/games'), {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(newgame),

  });
}
  

    return (
        <GamesContext.Provider value={{games, addGames}}>
            {children}
        </GamesContext.Provider>
    );
}
