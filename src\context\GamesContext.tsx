import { createContext, useEffect, useState, type ReactNode } from "react";
import type { IGames } from "../util/util";

interface IGameContext {
    games: IGames[];
    addGames: (newgame: IGames) => void;
    setGames: (games: IGames[]) => void;
}

const defaultIGameContext: IGameContext = {
    games: [],
    addGames: (_newgame: IGames) => {},
    setGames: (_games: IGames[]) => {}
}

export const GamesContext = createContext<IGameContext>(defaultIGameContext);

export function GamesProvider({children}: {children:ReactNode}) {
    const [games, setGames] = useState<IGames[]>([]);

    async function getGames() {
        let res = await fetch('http://localhost:3001/api/games').then(res => res.json());
        setGames(res);

    }
    useEffect(() => {
        getGames();
    }, []);


async function addGames(newgame: IGames) {
    try {
        const res = await fetch('http://localhost:3001/api/games', {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(newgame),
        });

        if (res.ok) {
            // Refresh the games list after adding a new game
            await getGames();
        }
    } catch (error) {
        console.error('Error adding game:', error);
    }
}

    return (
        <GamesContext.Provider value={{games, addGames, setGames}}>
            {children}
        </GamesContext.Provider>
    );
}
