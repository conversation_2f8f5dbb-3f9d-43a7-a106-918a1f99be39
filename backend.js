const games = [
    {
      "title": "The Witcher 3: Wild Hunt",
      "genre": "Action RPG",
      "developer": "CD Projekt Red",
      "publisher": "CD Projekt",
      "rating": 4.8
    },
    {
      "title": "God of War",
      "genre": "Action-Adventure",
      "developer": "Santa Monica Studio",
      "publisher": "Sony Interactive Entertainment",
      "rating": 4.9
    },
    {
      "title": "Elden Ring",
      "genre": "Action RPG",
      "developer": "FromSoftware",
      "publisher": "Bandai Namco Entertainment",
      "rating": 4.8
    },
    {
      "title": "Red Dead Redemption 2",
      "genre": "Action-Adventure",
      "developer": "Rockstar Games",
      "publisher": "Rockstar Games",
      "rating": 4.9
    },
    {
      "title": "Hollow Knight",
      "genre": "Metroidvania",
      "developer": "Team Cherry",
      "publisher": "Team Cherry",
      "rating": 4.7
    },
    {
      "title": "Hades",
      "genre": "Roguelike",
      "developer": "Supergiant Games",
      "publisher": "Supergiant Games",
      "rating": 4.8
    },
    {
      "title": "Dark Souls III",
      "genre": "Action RPG",
      "developer": "FromSoftware",
      "publisher": "Bandai Namco Entertainment",
      "rating": 4.6
    },
    {
      "title": "Super Mario Odyssey",
      "genre": "Platformer",
      "developer": "Nintendo EPD",
      "publisher": "Nintendo",
      "rating": 4.9
    },
    {
      "title": "Persona 5 Royal",
      "genre": "JRPG",
      "developer": "Atlus",
      "publisher": "Atlus",
      "rating": 4.9
    },
    {
      "title": "Ghost of Tsushima",
      "genre": "Action-Adventure",
      "developer": "Sucker Punch Productions",
      "publisher": "Sony Interactive Entertainment",
      "rating": 4.8
    },
    {
      "title": "Cyberpunk 2077",
      "genre": "Action RPG",
      "developer": "CD Projekt Red",
      "publisher": "CD Projekt",
      "rating": 4.2
    },
    {
      "title": "Celeste",
      "genre": "Platformer",
      "developer": "Matt Makes Games",
      "publisher": "Matt Makes Games",
      "rating": 4.7
    },
    {
      "title": "Stardew Valley",
      "genre": "Simulation RPG",
      "developer": "ConcernedApe",
      "publisher": "ConcernedApe",
      "rating": 4.8
    },
    {
      "title": "Bloodborne",
      "genre": "Action RPG",
      "developer": "FromSoftware",
      "publisher": "Sony Computer Entertainment",
      "rating": 4.8
    },
    {
      "title": "Sekiro: Shadows Die Twice",
      "genre": "Action-Adventure",
      "developer": "FromSoftware",
      "publisher": "Activision",
      "rating": 4.7
    }
  ]
  

import express, { json } from 'express'
import cors from 'cors'

const app = express();
app.use(cors())
app.use(json());

app.get('/api/games', async (req, res) => {
    res.status(200).send(games);
})
app.post('/api/games', async (req, res) => {
    try {
        const newGame = req.body;
        games.push({
          id: (games.length+1).toString(),
          title: newGame.title,
          rating: newGame.rating,
          genre: newGame.genre,
          developer: newGame.developer,
          publisher: newGame.publisher
        });
        res.status(201).send({success: true, message: "Game successfully added"})
    }
    catch(e){
        console.error(e);
        res.status(500).send({success: false, message: "Internal Server Error"})
    }
})

app.listen(3001, () => {
    console.log("Server is waiting requests on port 3001...")
})