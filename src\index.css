@import "tailwindcss";

body {
    @apply bg-gray-100 text-gray-800 font-sans m-0 p-0;
  }
  
  .page {
    @apply min-h-screen;
  }
  
  .navbar {
    @apply bg-blue-600 text-white p-4 flex justify-start gap-8 items-center shadow-md rounded-b-2xl w-full fixed z-10;
  }
  
  .navbar a {
    @apply bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-100 transition;
  }
  
  .navbar button {
    @apply p-0; /* Reset default Button padding if needed */
  }
  
  .games-container {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 p-12 pt-24 ;
  }
  
  .game-div {
    @apply text-lg bg-white shadow-lg rounded-2xl p-6 transition-transform transform hover:scale-105;
  }
  
  .game-title {
    @apply text-2xl font-bold mb-2;
  }
  .game-rating::before {
    content: '⭐ ';
    margin-right: 4px;
  }
  
  .game-rating {
    @apply text-yellow-600 font-semibold;
  }
  
  .game-genre,
  .game-developer,
  .game-publisher {
    @apply text-base text-gray-600 mt-1;
  }
  
  .new-game-form {
    @apply flex flex-col bg-white p-8 shadow-lg rounded-2xl max-w-xl mx-auto gap-6 pt-24;
  }
  
  .new-game-form h1 {
    @apply text-2xl font-bold mb-4;
  }
  
  .new-game-form input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400;
  }
  
  .new-game-form button {
    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition;
  }